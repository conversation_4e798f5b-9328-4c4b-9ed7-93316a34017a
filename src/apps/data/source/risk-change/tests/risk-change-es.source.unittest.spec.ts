import { Test, TestingModule } from '@nestjs/testing';
import { Client } from '@elastic/elasticsearch';
import { ConfigService } from 'libs/config/config.service';
import { DimensionHitDetailProcessor } from '../dimension-hit-detail.processor';
import { RelatedDimensionHitDetailProcessor } from '../related-dimension-hit-detail.processor';
import { CaseReasonHelper } from '../helper/case-reason.helper';
import { RiskChangeEsSource } from '../risk-change-es.source';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { DimensionHitResultPO } from 'libs/model/diligence/dimension/DimensionHitResultPO';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { DimensionDefinitionEntity } from 'libs/entities/DimensionDefinitionEntity';
import { DimensionSourceEnums } from 'libs/enums/diligence/DimensionSourceEnums';
import { CompanySearchService } from 'apps/company/company-search.service';
import { RiskChangeHelper } from 'apps/data/helper/risk.change.helper';

// 测试辅助函数
const createMockDimensionDefinition = (key: DimensionTypeEnums): DimensionDefinitionEntity => {
  const def = new DimensionDefinitionEntity();
  def.key = key;
  def.name = `测试维度-${key}`;
  def.source = DimensionSourceEnums.RiskChange;
  return def;
};

const createTestDimensionStrategy = (key: DimensionTypeEnums): DimensionHitStrategyPO => {
  const def = createMockDimensionDefinition(key);
  return new DimensionHitStrategyPO(def);
};

const createTestDimensionResult = (dimensionKey: DimensionTypeEnums, totalHits = 1): DimensionHitResultPO => {
  const result = new DimensionHitResultPO();
  result.dimensionKey = dimensionKey;
  result.dimensionName = `测试维度-${dimensionKey}`;
  result.totalHits = totalHits;
  result.description = '测试结果';
  return result;
};

describe('RiskChangeEsSource 单元测试', () => {
  let service: RiskChangeEsSource;
  let mockConfigService: jest.Mocked<ConfigService>;
  let mockRiskChangeHelper: jest.Mocked<RiskChangeHelper>;
  let mockCompanySearchService: jest.Mocked<CompanySearchService>;
  let mockDimensionHitDetailProcessor: jest.Mocked<DimensionHitDetailProcessor>;
  let mockRelatedDimensionHitDetailsProcessor: jest.Mocked<RelatedDimensionHitDetailProcessor>;
  let mockCaseReasonHelper: jest.Mocked<CaseReasonHelper>;
  let mockEsClient: jest.Mocked<Client>;

  beforeEach(async () => {
    // 创建 mock 对象
    mockConfigService = {
      esConfig: {
        riskChangeList: {
          nodes: ['http://localhost:9200'],
          indexName: 'test-risk-change-index',
        },
      },
    } as any;

    mockRiskChangeHelper = {
      hitMainInfoUpdateCapitalChange: jest.fn(),
    } as any;

    mockCompanySearchService = {
      searchCompany: jest.fn(),
    } as any;

    mockDimensionHitDetailProcessor = {
      fetchHits: jest.fn(),
      bindRiskChangeEsSearchFn: jest.fn(),
    } as any;

    mockRelatedDimensionHitDetailsProcessor = {
      processAnalyze: jest.fn(),
      fetchHits: jest.fn(),
      bindRiskChangeEsSearchFn: jest.fn(),
    } as any;

    mockCaseReasonHelper = {
      checkCaseTypeField: jest.fn(),
    } as any;

    mockEsClient = {
      search: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RiskChangeEsSource,
        { provide: ConfigService, useValue: mockConfigService },
        { provide: RiskChangeHelper, useValue: mockRiskChangeHelper },
        { provide: CompanySearchService, useValue: mockCompanySearchService },
        { provide: DimensionHitDetailProcessor, useValue: mockDimensionHitDetailProcessor },
        { provide: RelatedDimensionHitDetailProcessor, useValue: mockRelatedDimensionHitDetailsProcessor },
        { provide: CaseReasonHelper, useValue: mockCaseReasonHelper },
      ],
    }).compile();

    service = module.get<RiskChangeEsSource>(RiskChangeEsSource);

    // 手动设置 ES 客户端，因为它在构造函数中创建
    (service as any).esClient = mockEsClient;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('analyze 方法测试', () => {
    it('应该正确处理空的维度策略列表', async () => {
      // Arrange
      const companyId = 'test-company-id';
      const dimensionHitStrategyPOs: DimensionHitStrategyPO[] = [];

      // Act
      const result = await service.analyze(companyId, dimensionHitStrategyPOs);

      // Assert
      expect(result).toEqual([]);
    });

    it('应该正确分离关联方和非关联方维度', async () => {
      // Arrange
      const companyId = 'test-company-id';
      const relatedDimension = createTestDimensionStrategy(DimensionTypeEnums.ActualControllerRiskChange);
      const normalDimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);

      const dimensionHitStrategyPOs = [relatedDimension, normalDimension];

      // Mock 关联方处理器返回结果
      const mockRelatedResult = [createTestDimensionResult(DimensionTypeEnums.ActualControllerRiskChange, 1)];
      mockRelatedDimensionHitDetailsProcessor.processAnalyze.mockResolvedValue(mockRelatedResult);

      // Mock 父类 analyze 方法的行为
      jest.spyOn(service as any, 'analyzeByAggs').mockResolvedValue([createTestDimensionResult(DimensionTypeEnums.RiskChange, 1)]);

      // Act
      const result = await service.analyze(companyId, dimensionHitStrategyPOs);

      // Assert
      expect(mockRelatedDimensionHitDetailsProcessor.processAnalyze).toHaveBeenCalledWith([relatedDimension], companyId);
      expect(result).toHaveLength(2);
      expect(result[0].dimensionKey).toBe(DimensionTypeEnums.ActualControllerRiskChange);
      expect(result[1].dimensionKey).toBe(DimensionTypeEnums.RiskChange);
    });

    it('应该只处理关联方维度当没有普通维度时', async () => {
      // Arrange
      const companyId = 'test-company-id';
      const relatedDimension = createTestDimensionStrategy(DimensionTypeEnums.RecentInvestCancellationsRiskChange);

      const dimensionHitStrategyPOs = [relatedDimension];

      const mockRelatedResult = [createTestDimensionResult(DimensionTypeEnums.RecentInvestCancellationsRiskChange, 2)];
      mockRelatedDimensionHitDetailsProcessor.processAnalyze.mockResolvedValue(mockRelatedResult);

      // Act
      const result = await service.analyze(companyId, dimensionHitStrategyPOs);

      // Assert
      expect(mockRelatedDimensionHitDetailsProcessor.processAnalyze).toHaveBeenCalledWith([relatedDimension], companyId);
      expect(result).toEqual(mockRelatedResult);
    });
  });

  describe('getDimensionDetail 方法测试', () => {
    it('应该正确处理关联方维度详情查询', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.ListedEntityRiskChange);

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';
      params.pageIndex = 1;
      params.pageSize = 10;

      const mockResponse = new HitDetailsBaseResponse();
      mockResponse.Paging = {
        PageSize: 10,
        PageIndex: 1,
        TotalRecords: 5,
      };
      mockResponse.Result = [{ id: 'test-result' }];

      mockRelatedDimensionHitDetailsProcessor.fetchHits.mockResolvedValue(mockResponse);

      // Act
      const result = await service.getDimensionDetail(dimension, params);

      // Assert
      expect(mockRelatedDimensionHitDetailsProcessor.fetchHits).toHaveBeenCalledWith(dimension, params);
      expect(result).toEqual(mockResponse);
    });

    it('应该正确处理 RiskChange 维度详情查询', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';
      params.pageIndex = 1;
      params.pageSize = 10;

      // Mock 父类方法返回的基础数据
      const mockBaseResponse = new HitDetailsBaseResponse();
      mockBaseResponse.Result = [
        { id: 'item1', Category: 1 },
        { id: 'item2', Category: 2 },
      ];
      mockBaseResponse.Paging = {
        PageSize: 10,
        PageIndex: 1,
        TotalRecords: 2,
      };

      jest.spyOn(service as any, 'getDetailFromEs').mockResolvedValue(mockBaseResponse);

      // Mock 详情处理器返回的过滤结果
      const mockFilteredHits = [{ id: 'item1', Category: 1 }];
      mockDimensionHitDetailProcessor.fetchHits.mockResolvedValue(mockFilteredHits);

      // Act
      const result = await service.getDimensionDetail(dimension, params);

      // Assert
      expect(mockDimensionHitDetailProcessor.fetchHits).toHaveBeenCalledWith(mockBaseResponse, dimension, params);
      expect(result.Result).toEqual(mockFilteredHits);
      expect(result.Paging.TotalRecords).toBe(1); // 过滤后的数量
    });

    it('应该正确处理 MainInfoUpdateCapitalChange 维度详情查询', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.MainInfoUpdateCapitalChange);

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';
      params.pageIndex = 1;
      params.pageSize = 10;

      // Mock 父类方法返回的基础数据
      const mockBaseResponse = new HitDetailsBaseResponse();
      mockBaseResponse.Result = [
        { id: 'item1', Category: 37 },
        { id: 'item2', Category: 37 },
        { id: 'item3', Category: 37 },
      ];
      mockBaseResponse.Paging = {
        PageSize: 10,
        PageIndex: 1,
        TotalRecords: 3,
      };

      jest.spyOn(service as any, 'getDetailFromEs').mockResolvedValue(mockBaseResponse);

      // Mock helper 方法返回命中结果
      // @ts-ignore
      mockRiskChangeHelper.hitMainInfoUpdateCapitalChange.mockReturnValue([
        { id: 'item1', Category: 37 },
        { id: 'item3', Category: 37 },
      ]);

      // Act
      const result = await service.getDimensionDetail(dimension, params);

      // Assert
      expect(mockRiskChangeHelper.hitMainInfoUpdateCapitalChange).toHaveBeenCalledWith(mockBaseResponse.Result, dimension);
      expect(result.Result).toHaveLength(2);
      expect(result.Paging.TotalRecords).toBe(2);
    });
  });
});
