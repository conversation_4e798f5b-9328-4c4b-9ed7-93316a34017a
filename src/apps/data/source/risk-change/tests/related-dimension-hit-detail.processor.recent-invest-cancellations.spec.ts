import { Test, TestingModule } from '@nestjs/testing';
import { DimensionDefinitionEntity } from 'libs/entities/DimensionDefinitionEntity';
import { DimensionSourceEnums } from 'libs/enums/diligence/DimensionSourceEnums';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionHitDetailProcessor } from '../dimension-hit-detail.processor';
import { CaseReasonHelper } from '../helper/case-reason.helper';
import { RelatedDimensionHitDetailProcessor } from '../related-dimension-hit-detail.processor';
import { CompanyDetailService } from 'apps/company/company-detail.service';
import { CompanySearchService } from 'apps/company/company-search.service';
import { PersonHelper } from 'apps/data/helper/person.helper';
import { RiskChangeHelper } from 'apps/data/helper/risk.change.helper';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { RelatedTypeEnums } from 'libs/enums/dimension/RelatedTypeEnums';
import { DimensionFieldCompareTypeEnums } from 'libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { DimensionHitStrategyFieldsEntity } from 'libs/entities/DimensionHitStrategyFieldsEntity';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';

// 测试辅助函数
const createTestDimensionStrategy = (key: DimensionTypeEnums): DimensionHitStrategyPO => {
  const def = new DimensionDefinitionEntity();
  def.key = key;
  def.name = `测试维度-${key}`;
  def.source = DimensionSourceEnums.RiskChange;
  return new DimensionHitStrategyPO(def);
};

const createMockStrategyField = (
  fieldKey: DimensionFieldKeyEnums,
  fieldValue: any[],
  compareType?: DimensionFieldCompareTypeEnums,
): DimensionHitStrategyFieldsEntity => {
  const field = new DimensionHitStrategyFieldsEntity();
  field.dimensionFieldKey = fieldKey;
  field.fieldValue = fieldValue;
  field.compareType = compareType;
  return field;
};

describe('RelatedDimensionHitDetailProcessor RecentInvestCancellationsRiskChange 维度测试', () => {
  let processor: RelatedDimensionHitDetailProcessor;
  let mockPersonHelper: jest.Mocked<PersonHelper>;
  let mockCompanyDetailService: jest.Mocked<CompanyDetailService>;
  let mockCompanySearchService: jest.Mocked<CompanySearchService>;
  let mockRiskChangeHitDetailAnalyzer: jest.Mocked<DimensionHitDetailProcessor>;
  let mockRiskChangeHelper: jest.Mocked<RiskChangeHelper>;
  let mockCaseReasonHelper: jest.Mocked<CaseReasonHelper>;
  let mockSearchEs: jest.Mock;

  beforeEach(async () => {
    // 创建 mock 对象
    mockPersonHelper = {
      getFinalActualController: jest.fn(),
      getPartnerList: jest.fn(),
    } as any;

    mockCompanyDetailService = {
      getInvestCompany: jest.fn(),
    } as any;

    mockCompanySearchService = {
      companyDetailsQcc: jest.fn(),
    } as any;

    mockRiskChangeHitDetailAnalyzer = {
      commonCivilRiskChange: jest.fn(),
    } as any;

    mockRiskChangeHelper = {
      detailAnalyzeForRelated: jest.fn(),
      hitIndustryThresholdField: jest.fn(),
    } as any;

    mockCaseReasonHelper = {
      getCaseTitleDescData: jest.fn(),
    } as any;

    mockSearchEs = jest.fn();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RelatedDimensionHitDetailProcessor,
        { provide: PersonHelper, useValue: mockPersonHelper },
        { provide: CompanyDetailService, useValue: mockCompanyDetailService },
        { provide: CompanySearchService, useValue: mockCompanySearchService },
        { provide: DimensionHitDetailProcessor, useValue: mockRiskChangeHitDetailAnalyzer },
        { provide: RiskChangeHelper, useValue: mockRiskChangeHelper },
        { provide: CaseReasonHelper, useValue: mockCaseReasonHelper },
      ],
    }).compile();

    processor = module.get<RelatedDimensionHitDetailProcessor>(RelatedDimensionHitDetailProcessor);
    processor.bindRiskChangeEsSearchFn(mockSearchEs);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('RecentInvestCancellationsRiskChange 维度处理', () => {
    it('应该正确处理时间周期过滤', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RecentInvestCancellationsRiskChange);
      dimension.strategyFields = [
        createMockStrategyField(DimensionFieldKeyEnums.relatedRoleType, [RelatedTypeEnums.InvestCompany]),
        createMockStrategyField(DimensionFieldKeyEnums.riskCategories, ['category1']),
        createMockStrategyField(DimensionFieldKeyEnums.timePeriod, [6]), // 近6个月
      ];

      // Mock 对外投资企业
      mockCompanyDetailService.getInvestCompany.mockResolvedValue({
        Paging: { TotalRecords: 5 },
        Result: [{ KeyNo: 'invest1' }, { KeyNo: 'invest2' }, { KeyNo: 'invest3' }, { KeyNo: 'invest4' }, { KeyNo: 'invest5' }],
      });

      // Mock ES查询结果
      mockSearchEs.mockResolvedValue({
        body: {
          hits: {
            total: { value: 3 },
            hits: [{ _source: { id: 'hit1', KeyNo: 'invest1' } }, { _source: { id: 'hit2', KeyNo: 'invest2' } }, { _source: { id: 'hit3', KeyNo: 'invest3' } }],
          },
        },
      });

      // Mock 时间周期查询
      const mockPeriodResponse = {
        Paging: { PageSize: 10, PageIndex: 1, TotalRecords: 2 },
        Result: [
          { id: 'period1', KeyNo: 'invest1' },
          { id: 'period2', KeyNo: 'invest2' },
        ],
      } as any;
      mockRiskChangeHitDetailAnalyzer.commonCivilRiskChange.mockResolvedValue(mockPeriodResponse);

      // Mock 关联方过滤
      mockRiskChangeHelper.detailAnalyzeForRelated.mockResolvedValue([
        { id: 'filtered1', KeyNo: 'invest1' },
        { id: 'filtered2', KeyNo: 'invest2' },
      ]);

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 1,
        pageSize: 10,
      };

      // Act
      const result = await processor.fetchHits(dimension, params);

      // Assert
      expect(mockRiskChangeHitDetailAnalyzer.commonCivilRiskChange).toHaveBeenCalledWith(
        ['invest1', 'invest2', 'invest3', 'invest4', 'invest5'],
        ['category1'],
        6,
        'month',
        10000,
      );
      expect(mockRiskChangeHelper.detailAnalyzeForRelated).toHaveBeenCalledWith(
        [
          { id: 'period1', KeyNo: 'invest1' },
          { id: 'period2', KeyNo: 'invest2' },
        ],
        dimension,
        params,
      );
    });

    it('应该正确处理阈值规则验证', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RecentInvestCancellationsRiskChange);
      dimension.strategyFields = [
        createMockStrategyField(DimensionFieldKeyEnums.relatedRoleType, [RelatedTypeEnums.InvestCompany]),
        createMockStrategyField(DimensionFieldKeyEnums.riskCategories, ['category1']),
        createMockStrategyField(DimensionFieldKeyEnums.fundedRatioLevel, [3]), // >20%
        createMockStrategyField(DimensionFieldKeyEnums.thresholdRule, [
          {
            investCount: [10, 50], // 投资企业数量在10-50之间
            threshold: 20, // 阈值20%
          },
        ]),
      ];

      // Mock 对外投资企业 - 总共30家
      mockCompanyDetailService.getInvestCompany
        .mockResolvedValueOnce({
          Paging: { TotalRecords: 30 },
          Result: Array.from({ length: 30 }, (_, i) => ({ KeyNo: `invest${i + 1}` })),
        })
        .mockResolvedValueOnce({
          Paging: { TotalRecords: 30 },
        });

      // Mock ES查询结果
      mockSearchEs.mockResolvedValue({
        body: {
          hits: {
            total: { value: 8 },
            hits: Array.from({ length: 8 }, (_, i) => ({ _source: { id: `hit${i + 1}` } })),
          },
        },
      });

      // Mock 关联方过滤 - 返回6家注销企业
      mockRiskChangeHelper.detailAnalyzeForRelated.mockResolvedValue(Array.from({ length: 6 }, (_, i) => ({ id: `filtered${i + 1}` })));

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 1,
        pageSize: 10,
      };

      // Act
      const result = await processor.fetchHits(dimension, params);

      // Assert
      // 6/30 = 20% >= 20% 阈值，应该命中
      expect(result.Paging.TotalRecords).toBe(6);
      expect(result.Result).toHaveLength(6);
    });

    it('应该正确处理不满足阈值规则的情况', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RecentInvestCancellationsRiskChange);
      dimension.strategyFields = [
        createMockStrategyField(DimensionFieldKeyEnums.relatedRoleType, [RelatedTypeEnums.InvestCompany]),
        createMockStrategyField(DimensionFieldKeyEnums.riskCategories, ['category1']),
        createMockStrategyField(DimensionFieldKeyEnums.fundedRatioLevel, [3]),
        createMockStrategyField(DimensionFieldKeyEnums.thresholdRule, [
          {
            investCount: [10, 50],
            threshold: 30, // 阈值30%
          },
        ]),
      ];

      // Mock 对外投资企业 - 总共30家
      mockCompanyDetailService.getInvestCompany
        .mockResolvedValueOnce({
          Paging: { TotalRecords: 30 },
          Result: Array.from({ length: 30 }, (_, i) => ({ KeyNo: `invest${i + 1}` })),
        })
        .mockResolvedValueOnce({
          Paging: { TotalRecords: 30 },
        });

      // Mock ES查询结果
      mockSearchEs.mockResolvedValue({
        body: {
          hits: {
            total: { value: 5 },
            hits: Array.from({ length: 5 }, (_, i) => ({ _source: { id: `hit${i + 1}` } })),
          },
        },
      });

      // Mock 关联方过滤 - 返回5家注销企业
      mockRiskChangeHelper.detailAnalyzeForRelated.mockResolvedValue(Array.from({ length: 5 }, (_, i) => ({ id: `filtered${i + 1}` })));

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 1,
        pageSize: 10,
      };

      // Act
      const result = await processor.fetchHits(dimension, params);

      // Assert
      // 5/30 = 16.67% < 30% 阈值，不应该命中
      expect(result.Paging.TotalRecords).toBe(0);
    });

    it('应该正确处理投资企业数量不在阈值规则范围内的情况', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RecentInvestCancellationsRiskChange);
      dimension.strategyFields = [
        createMockStrategyField(DimensionFieldKeyEnums.relatedRoleType, [RelatedTypeEnums.InvestCompany]),
        createMockStrategyField(DimensionFieldKeyEnums.riskCategories, ['category1']),
        createMockStrategyField(DimensionFieldKeyEnums.thresholdRule, [
          {
            investCount: [10, 50], // 投资企业数量需要在10-50之间
            threshold: 20,
          },
        ]),
      ];

      // Mock 对外投资企业 - 只有5家，不满足最小数量要求
      mockCompanyDetailService.getInvestCompany
        .mockResolvedValueOnce({
          Paging: { TotalRecords: 5 },
          Result: Array.from({ length: 5 }, (_, i) => ({ KeyNo: `invest${i + 1}` })),
        })
        .mockResolvedValueOnce({
          Paging: { TotalRecords: 5 },
        });

      // Mock ES查询结果
      mockSearchEs.mockResolvedValue({
        body: {
          hits: {
            total: { value: 2 },
            hits: [{ _source: { id: 'hit1' } }, { _source: { id: 'hit2' } }],
          },
        },
      });

      // Mock 关联方过滤
      mockRiskChangeHelper.detailAnalyzeForRelated.mockResolvedValue([{ id: 'filtered1' }, { id: 'filtered2' }]);

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 1,
        pageSize: 10,
      };

      // Act
      const result = await processor.fetchHits(dimension, params);

      // Assert
      // 投资企业数量5不在[10,50]范围内，不应该命中
      expect(result.Paging.TotalRecords).toBe(0);
    });

    it('应该正确处理命中数量字段验证', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RecentInvestCancellationsRiskChange);
      dimension.strategyFields = [
        createMockStrategyField(DimensionFieldKeyEnums.relatedRoleType, [RelatedTypeEnums.InvestCompany]),
        createMockStrategyField(DimensionFieldKeyEnums.riskCategories, ['category1']),
        createMockStrategyField(DimensionFieldKeyEnums.hitCount, [20], DimensionFieldCompareTypeEnums.GreaterThan),
      ];

      // Mock 对外投资企业 - 总共30家
      mockCompanyDetailService.getInvestCompany
        .mockResolvedValueOnce({
          Paging: { TotalRecords: 30 },
          Result: Array.from({ length: 30 }, (_, i) => ({ KeyNo: `invest${i + 1}` })),
        })
        .mockResolvedValueOnce({
          Paging: { TotalRecords: 30 },
        });

      // Mock ES查询结果
      mockSearchEs.mockResolvedValue({
        body: {
          hits: {
            total: { value: 15 },
            hits: Array.from({ length: 15 }, (_, i) => ({ _source: { id: `hit${i + 1}` } })),
          },
        },
      });

      // Mock 关联方过滤
      mockRiskChangeHelper.detailAnalyzeForRelated.mockResolvedValue(Array.from({ length: 15 }, (_, i) => ({ id: `filtered${i + 1}` })));

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 1,
        pageSize: 10,
      };

      // Act
      const result = await processor.fetchHits(dimension, params);

      // Assert
      // 投资企业数量30 > 20，不满足命中数量要求，不应该命中
      expect(result.Paging.TotalRecords).toBe(0);
    });

    it('应该正确处理没有对外投资企业的情况', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RecentInvestCancellationsRiskChange);
      dimension.strategyFields = [
        createMockStrategyField(DimensionFieldKeyEnums.relatedRoleType, [RelatedTypeEnums.InvestCompany]),
        createMockStrategyField(DimensionFieldKeyEnums.riskCategories, ['category1']),
      ];

      // Mock 没有对外投资企业
      mockCompanyDetailService.getInvestCompany.mockResolvedValue({
        Paging: { TotalRecords: 0 },
        Result: [],
      });

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 1,
        pageSize: 10,
      };

      // Act
      const result = await processor.fetchHits(dimension, params);

      // Assert
      expect(result.Paging.TotalRecords).toBe(0);
      expect(mockSearchEs).not.toHaveBeenCalled();
    });
  });
});
