import { Test, TestingModule } from '@nestjs/testing';
import { DimensionDefinitionEntity } from 'libs/entities/DimensionDefinitionEntity';
import { DimensionSourceEnums } from 'libs/enums/diligence/DimensionSourceEnums';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionHitDetailProcessor } from '../dimension-hit-detail.processor';
import { CaseReasonHelper } from '../helper/case-reason.helper';
import { RelatedDimensionHitDetailProcessor } from '../related-dimension-hit-detail.processor';
import { CompanyDetailService } from 'apps/company/company-detail.service';
import { CompanySearchService } from 'apps/company/company-search.service';
import { PersonHelper } from 'apps/data/helper/person.helper';
import { RiskChangeHelper } from 'apps/data/helper/risk.change.helper';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { RelatedTypeEnums } from 'libs/enums/dimension/RelatedTypeEnums';
import { DimensionFieldCompareTypeEnums } from 'libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { DimensionHitStrategyFieldsEntity } from 'libs/entities/DimensionHitStrategyFieldsEntity';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';

// 测试辅助函数
const createTestDimensionStrategy = (key: DimensionTypeEnums): DimensionHitStrategyPO => {
  const def = new DimensionDefinitionEntity();
  def.key = key;
  def.name = `测试维度-${key}`;
  def.source = DimensionSourceEnums.RiskChange;
  return new DimensionHitStrategyPO(def);
};

const createMockStrategyField = (
  fieldKey: DimensionFieldKeyEnums,
  fieldValue: any[],
  compareType?: DimensionFieldCompareTypeEnums,
): DimensionHitStrategyFieldsEntity => {
  const field = new DimensionHitStrategyFieldsEntity();
  field.dimensionFieldKey = fieldKey;
  field.fieldValue = fieldValue;
  field.compareType = compareType;
  return field;
};

describe('RelatedDimensionHitDetailProcessor ActualControllerRiskChange 维度测试', () => {
  let processor: RelatedDimensionHitDetailProcessor;
  let mockPersonHelper: jest.Mocked<PersonHelper>;
  let mockCompanyDetailService: jest.Mocked<CompanyDetailService>;
  let mockCompanySearchService: jest.Mocked<CompanySearchService>;
  let mockRiskChangeHitDetailAnalyzer: jest.Mocked<DimensionHitDetailProcessor>;
  let mockRiskChangeHelper: jest.Mocked<RiskChangeHelper>;
  let mockCaseReasonHelper: jest.Mocked<CaseReasonHelper>;
  let mockSearchEs: jest.Mock;

  beforeEach(async () => {
    // 创建 mock 对象
    mockPersonHelper = {
      getFinalActualController: jest.fn(),
      getPartnerList: jest.fn(),
    } as any;

    mockCompanyDetailService = {
      getInvestCompany: jest.fn(),
    } as any;

    mockCompanySearchService = {
      companyDetailsQcc: jest.fn(),
    } as any;

    mockRiskChangeHitDetailAnalyzer = {
      commonCivilRiskChange: jest.fn(),
    } as any;

    mockRiskChangeHelper = {
      detailAnalyzeForRelated: jest.fn(),
      hitIndustryThresholdField: jest.fn(),
    } as any;

    mockCaseReasonHelper = {
      getCaseTitleDescData: jest.fn(),
    } as any;

    mockSearchEs = jest.fn();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RelatedDimensionHitDetailProcessor,
        { provide: PersonHelper, useValue: mockPersonHelper },
        { provide: CompanyDetailService, useValue: mockCompanyDetailService },
        { provide: CompanySearchService, useValue: mockCompanySearchService },
        { provide: DimensionHitDetailProcessor, useValue: mockRiskChangeHitDetailAnalyzer },
        { provide: RiskChangeHelper, useValue: mockRiskChangeHelper },
        { provide: CaseReasonHelper, useValue: mockCaseReasonHelper },
      ],
    }).compile();

    processor = module.get<RelatedDimensionHitDetailProcessor>(RelatedDimensionHitDetailProcessor);
    processor.bindRiskChangeEsSearchFn(mockSearchEs);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('ActualControllerRiskChange 维度处理', () => {
    it('应该正确处理时间周期过滤', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.ActualControllerRiskChange);
      dimension.strategyFields = [
        createMockStrategyField(DimensionFieldKeyEnums.relatedRoleType, [RelatedTypeEnums.ActualController]),
        createMockStrategyField(DimensionFieldKeyEnums.riskCategories, ['category1']),
        createMockStrategyField(DimensionFieldKeyEnums.timePeriod, [12]), // 近12个月
      ];

      // Mock 实际控制人
      mockPersonHelper.getFinalActualController.mockResolvedValue([
        { keyNo: 'person1', name: '实控人1' },
        { keyNo: 'person2', name: '实控人2' },
      ]);

      // Mock ES查询结果
      mockSearchEs.mockResolvedValue({
        body: {
          hits: {
            total: { value: 5 },
            hits: Array.from({ length: 5 }, (_, i) => ({ _source: { id: `hit${i + 1}` } })),
          },
        },
      });

      // Mock 时间周期查询
      const mockPeriodResponse = {
        Paging: { PageSize: 10, PageIndex: 1, TotalRecords: 3 },
        Result: [
          { id: 'period1', KeyNo: 'person1' },
          { id: 'period2', KeyNo: 'person2' },
          { id: 'period3', KeyNo: 'person1' },
        ],
      } as any;
      mockRiskChangeHitDetailAnalyzer.commonCivilRiskChange.mockResolvedValue(mockPeriodResponse);

      // Mock 关联方过滤
      mockRiskChangeHelper.detailAnalyzeForRelated.mockResolvedValue([
        { id: 'filtered1', KeyNo: 'person1' },
        { id: 'filtered2', KeyNo: 'person2' },
        { id: 'filtered3', KeyNo: 'person1' },
      ]);

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 1,
        pageSize: 10,
      };

      // Act
      const result = await processor.fetchHits(dimension, params);

      // Assert
      expect(mockRiskChangeHitDetailAnalyzer.commonCivilRiskChange).toHaveBeenCalledWith(['person1', 'person2'], ['category1'], 12, 'month', 10000);
      expect(mockRiskChangeHelper.detailAnalyzeForRelated).toHaveBeenCalledWith(
        [
          { id: 'period1', KeyNo: 'person1' },
          { id: 'period2', KeyNo: 'person2' },
          { id: 'period3', KeyNo: 'person1' },
        ],
        dimension,
        params,
      );
    });

    it('应该正确处理阈值数量验证', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.ActualControllerRiskChange);
      dimension.strategyFields = [
        createMockStrategyField(DimensionFieldKeyEnums.relatedRoleType, [RelatedTypeEnums.ActualController]),
        createMockStrategyField(DimensionFieldKeyEnums.riskCategories, ['category1']),
        createMockStrategyField(DimensionFieldKeyEnums.thresholdCount, [5], DimensionFieldCompareTypeEnums.GreaterThan),
      ];

      // Mock 实际控制人
      mockPersonHelper.getFinalActualController.mockResolvedValue([{ keyNo: 'person1', name: '实控人1' }]);

      // Mock ES查询结果
      mockSearchEs.mockResolvedValue({
        body: {
          hits: {
            total: { value: 8 },
            hits: Array.from({ length: 8 }, (_, i) => ({ _source: { id: `hit${i + 1}` } })),
          },
        },
      });

      // Mock 关联方过滤 - 返回7条记录
      mockRiskChangeHelper.detailAnalyzeForRelated.mockResolvedValue(Array.from({ length: 7 }, (_, i) => ({ id: `filtered${i + 1}` })));

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 1,
        pageSize: 10,
      };

      // Act
      const result = await processor.fetchHits(dimension, params);

      // Assert
      // 7 > 5，满足阈值条件，应该命中
      expect(result.Paging.TotalRecords).toBe(7);
      expect(result.Result).toHaveLength(7);
    });

    it('应该正确处理不满足阈值数量的情况', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.ActualControllerRiskChange);
      dimension.strategyFields = [
        createMockStrategyField(DimensionFieldKeyEnums.relatedRoleType, [RelatedTypeEnums.ActualController]),
        createMockStrategyField(DimensionFieldKeyEnums.riskCategories, ['category1']),
        createMockStrategyField(DimensionFieldKeyEnums.thresholdCount, [10], DimensionFieldCompareTypeEnums.GreaterThan),
      ];

      // Mock 实际控制人
      mockPersonHelper.getFinalActualController.mockResolvedValue([{ keyNo: 'person1', name: '实控人1' }]);

      // Mock ES查询结果
      mockSearchEs.mockResolvedValue({
        body: {
          hits: {
            total: { value: 5 },
            hits: Array.from({ length: 5 }, (_, i) => ({ _source: { id: `hit${i + 1}` } })),
          },
        },
      });

      // Mock 关联方过滤 - 返回5条记录
      mockRiskChangeHelper.detailAnalyzeForRelated.mockResolvedValue(Array.from({ length: 5 }, (_, i) => ({ id: `filtered${i + 1}` })));

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 1,
        pageSize: 10,
      };

      // Act
      const result = await processor.fetchHits(dimension, params);

      // Assert
      // 5 <= 10，不满足阈值条件，不应该命中
      expect(result.Paging.TotalRecords).toBe(0);
    });

    it('应该正确处理行业阈值验证', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.ActualControllerRiskChange);
      dimension.strategyFields = [
        createMockStrategyField(DimensionFieldKeyEnums.relatedRoleType, [RelatedTypeEnums.ActualController]),
        createMockStrategyField(DimensionFieldKeyEnums.riskCategories, ['category1']),
        createMockStrategyField(DimensionFieldKeyEnums.industryThreshold, [{ industry: 'IT', threshold: 3 }]),
      ];

      // Mock 实际控制人
      mockPersonHelper.getFinalActualController.mockResolvedValue([{ keyNo: 'person1', name: '实控人1' }]);

      // Mock ES查询结果
      mockSearchEs.mockResolvedValue({
        body: {
          hits: {
            total: { value: 5 },
            hits: Array.from({ length: 5 }, (_, i) => ({ _source: { id: `hit${i + 1}` } })),
          },
        },
      });

      // Mock 关联方过滤
      const mockFilteredData = Array.from({ length: 5 }, (_, i) => ({ id: `filtered${i + 1}` }));
      mockRiskChangeHelper.detailAnalyzeForRelated.mockResolvedValue(mockFilteredData);

      // Mock 行业阈值验证 - 返回true表示满足行业阈值
      mockRiskChangeHelper.hitIndustryThresholdField.mockResolvedValue(true);

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 1,
        pageSize: 10,
      };

      // Act
      const result = await processor.fetchHits(dimension, params);

      // Assert
      expect(mockRiskChangeHelper.hitIndustryThresholdField).toHaveBeenCalledWith(
        dimension.strategyFields.find((f) => f.dimensionFieldKey === DimensionFieldKeyEnums.industryThreshold),
        mockFilteredData,
        'test-company-id',
      );
      expect(result.Paging.TotalRecords).toBe(5);
    });

    it('应该正确处理不满足行业阈值的情况', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.ActualControllerRiskChange);
      dimension.strategyFields = [
        createMockStrategyField(DimensionFieldKeyEnums.relatedRoleType, [RelatedTypeEnums.ActualController]),
        createMockStrategyField(DimensionFieldKeyEnums.riskCategories, ['category1']),
        createMockStrategyField(DimensionFieldKeyEnums.industryThreshold, [{ industry: 'IT', threshold: 10 }]),
      ];

      // Mock 实际控制人
      mockPersonHelper.getFinalActualController.mockResolvedValue([{ keyNo: 'person1', name: '实控人1' }]);

      // Mock ES查询结果
      mockSearchEs.mockResolvedValue({
        body: {
          hits: {
            total: { value: 3 },
            hits: Array.from({ length: 3 }, (_, i) => ({ _source: { id: `hit${i + 1}` } })),
          },
        },
      });

      // Mock 关联方过滤
      const mockFilteredData = Array.from({ length: 3 }, (_, i) => ({ id: `filtered${i + 1}` }));
      mockRiskChangeHelper.detailAnalyzeForRelated.mockResolvedValue(mockFilteredData);

      // Mock 行业阈值验证 - 返回false表示不满足行业阈值
      mockRiskChangeHelper.hitIndustryThresholdField.mockResolvedValue(false);

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 1,
        pageSize: 10,
      };

      // Act
      const result = await processor.fetchHits(dimension, params);

      // Assert
      expect(result.Paging.TotalRecords).toBe(0);
    });

    it('应该正确处理默认比较类型', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.ActualControllerRiskChange);
      dimension.strategyFields = [
        createMockStrategyField(DimensionFieldKeyEnums.relatedRoleType, [RelatedTypeEnums.ActualController]),
        createMockStrategyField(DimensionFieldKeyEnums.riskCategories, ['category1']),
        createMockStrategyField(DimensionFieldKeyEnums.thresholdCount, [5]), // 没有指定compareType
      ];

      // Mock 实际控制人
      mockPersonHelper.getFinalActualController.mockResolvedValue([{ keyNo: 'person1', name: '实控人1' }]);

      // Mock ES查询结果
      mockSearchEs.mockResolvedValue({
        body: {
          hits: {
            total: { value: 8 },
            hits: Array.from({ length: 8 }, (_, i) => ({ _source: { id: `hit${i + 1}` } })),
          },
        },
      });

      // Mock 关联方过滤 - 返回6条记录
      mockRiskChangeHelper.detailAnalyzeForRelated.mockResolvedValue(Array.from({ length: 6 }, (_, i) => ({ id: `filtered${i + 1}` })));

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 1,
        pageSize: 10,
      };

      // Act
      const result = await processor.fetchHits(dimension, params);

      // Assert
      // 默认使用 GreaterThan，6 > 5，应该命中
      expect(result.Paging.TotalRecords).toBe(6);
    });

    it('应该正确处理没有实际控制人的情况', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.ActualControllerRiskChange);
      dimension.strategyFields = [
        createMockStrategyField(DimensionFieldKeyEnums.relatedRoleType, [RelatedTypeEnums.ActualController]),
        createMockStrategyField(DimensionFieldKeyEnums.riskCategories, ['category1']),
      ];

      // Mock 没有实际控制人
      mockPersonHelper.getFinalActualController.mockResolvedValue([]);

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 1,
        pageSize: 10,
      };

      // Act
      const result = await processor.fetchHits(dimension, params);

      // Assert
      expect(result.Paging.TotalRecords).toBe(0);
      expect(mockSearchEs).not.toHaveBeenCalled();
    });

    it('应该正确处理没有时间周期字段时使用ES查询结果', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.ActualControllerRiskChange);
      dimension.strategyFields = [
        createMockStrategyField(DimensionFieldKeyEnums.relatedRoleType, [RelatedTypeEnums.ActualController]),
        createMockStrategyField(DimensionFieldKeyEnums.riskCategories, ['category1']),
        // 没有 timePeriod 字段
      ];

      // Mock 实际控制人
      mockPersonHelper.getFinalActualController.mockResolvedValue([{ keyNo: 'person1', name: '实控人1' }]);

      // Mock ES查询结果
      const mockEsData = [
        { id: 'es1', KeyNo: 'person1' },
        { id: 'es2', KeyNo: 'person1' },
      ];
      mockSearchEs.mockResolvedValue({
        body: {
          hits: {
            total: { value: 2 },
            hits: mockEsData.map((d) => ({ _source: d })),
          },
        },
      });

      // Mock 关联方过滤
      mockRiskChangeHelper.detailAnalyzeForRelated.mockResolvedValue(mockEsData);

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 1,
        pageSize: 10,
      };

      // Act
      const result = await processor.fetchHits(dimension, params);

      // Assert
      expect(mockRiskChangeHitDetailAnalyzer.commonCivilRiskChange).not.toHaveBeenCalled();
      expect(mockRiskChangeHelper.detailAnalyzeForRelated).toHaveBeenCalledWith(mockEsData, dimension, params);
      expect(result.Paging.TotalRecords).toBe(2);
    });
  });
});
