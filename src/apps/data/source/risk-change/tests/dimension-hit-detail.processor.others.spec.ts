import { Test, TestingModule } from '@nestjs/testing';
import { DimensionHitDetailProcessor } from '../dimension-hit-detail.processor';
import { BaseHelper } from '../helper/base.helper';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { RiskChangeCategoryEnum } from 'libs/enums/riskchange/RiskChangeCategoryEnum';
import { DimensionDefinitionEntity } from 'libs/entities/DimensionDefinitionEntity';
import { DimensionSourceEnums } from 'libs/enums/diligence/DimensionSourceEnums';
import { PersonHelper } from 'apps/data/helper/person.helper';
import { RiskChangeHelper } from 'apps/data/helper/risk.change.helper';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { DimensionHitStrategyFieldsEntity } from 'libs/entities/DimensionHitStrategyFieldsEntity';

// 测试辅助函数
const createTestDimensionStrategy = (key: DimensionTypeEnums): DimensionHitStrategyPO => {
  const def = new DimensionDefinitionEntity();
  def.key = key;
  def.name = `测试维度-${key}`;
  def.source = DimensionSourceEnums.RiskChange;
  return new DimensionHitStrategyPO(def);
};

const createTestStrategyField = (fieldKey: DimensionFieldKeyEnums, fieldValue: any): DimensionHitStrategyFieldsEntity => {
  const field = new DimensionHitStrategyFieldsEntity();
  field.dimensionFieldKey = fieldKey;
  field.fieldValue = fieldValue;
  return field;
};

describe('DimensionHitDetailProcessor 其他类型测试', () => {
  let processor: DimensionHitDetailProcessor;
  let mockRiskChangeHelper: jest.Mocked<RiskChangeHelper>;
  let mockPersonHelper: jest.Mocked<PersonHelper>;
  let mockBaseHelper: jest.Mocked<BaseHelper>;
  let mockSearchEs: jest.Mock;

  beforeEach(async () => {
    // 创建 mock 对象
    mockRiskChangeHelper = {
      category86IntellectualRole: jest.fn(),
      category86IntellectualType: jest.fn(),
      category79Field: jest.fn(),
      category23Field: jest.fn(),
      category28Field: jest.fn(),
      categoryAnnouncementReportField: jest.fn(),
      category114shareChangeStatusField: jest.fn(),
      category114holderRoleField: jest.fn(),
      category114beforeContentField: jest.fn(),
      category114afterContentField: jest.fn(),
      hitChangeStatusField: jest.fn(),
      hitTimePeriodThresholdCountField: jest.fn(),
    } as any;

    mockPersonHelper = {
      getEmployeeData: jest.fn(),
    } as any;

    mockBaseHelper = {
      filterLastYearData: jest.fn(),
      hitNegativePositiveNewsField: jest.fn(),
    } as any;

    mockSearchEs = jest.fn();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DimensionHitDetailProcessor,
        { provide: RiskChangeHelper, useValue: mockRiskChangeHelper },
        { provide: PersonHelper, useValue: mockPersonHelper },
        { provide: BaseHelper, useValue: mockBaseHelper },
      ],
    }).compile();

    processor = module.get<DimensionHitDetailProcessor>(DimensionHitDetailProcessor);
    processor.bindRiskChangeEsSearchFn(mockSearchEs);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('知识产权出质 (category86)', () => {
    it('应该正确处理知识产权出质类型的风险动态', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-intellectual-property-pledge',
        Category: RiskChangeCategoryEnum.category86,
        ChangeExtend: JSON.stringify({
          IntellectualType: '专利',
          Role: '出质人',
          PledgeAmount: '5000000',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const intellectualRoleField = createTestStrategyField(DimensionFieldKeyEnums.intellectualRole, ['出质人']);
      const intellectualTypeField = createTestStrategyField(DimensionFieldKeyEnums.intellectualType, ['专利']);
      dimension.strategyFields = [intellectualRoleField, intellectualTypeField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.category86IntellectualRole.mockReturnValue(true);
      mockRiskChangeHelper.category86IntellectualType.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-intellectual-property-pledge');
      expect(mockRiskChangeHelper.category86IntellectualRole).toHaveBeenCalledWith(
        intellectualRoleField,
        expect.objectContaining({
          Id: 'test-intellectual-property-pledge',
          Category: RiskChangeCategoryEnum.category86,
        }),
      );
      expect(mockRiskChangeHelper.category86IntellectualType).toHaveBeenCalledWith(
        intellectualTypeField,
        expect.objectContaining({
          Id: 'test-intellectual-property-pledge',
          Category: RiskChangeCategoryEnum.category86,
        }),
      );
    });
  });

  describe('产品召回 (category79)', () => {
    it('应该正确处理产品召回类型的风险动态', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-product-recall',
        Category: RiskChangeCategoryEnum.category79,
        ChangeExtend: JSON.stringify({
          ProductName: '测试产品',
          RecallReason: '质量缺陷',
          RecallQuantity: '10000',
          RecallDate: '2024-01-20',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const recallReasonField = createTestStrategyField(DimensionFieldKeyEnums.layTypes, ['质量缺陷']);
      dimension.strategyFields = [recallReasonField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.category79Field.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-product-recall');
      expect(mockRiskChangeHelper.category79Field).toHaveBeenCalledWith(
        recallReasonField,
        expect.objectContaining({
          Id: 'test-product-recall',
          Category: RiskChangeCategoryEnum.category79,
        }),
      );
    });
  });

  describe('清算信息 (category23)', () => {
    it('应该正确处理清算信息类型的风险动态', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-liquidation-info',
        Category: RiskChangeCategoryEnum.category23,
        ChangeExtend: JSON.stringify({
          LiquidationType: '自愿清算',
          LiquidationReason: '公司解散',
          LiquidationDate: '2024-02-15',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const liquidationTypeField = createTestStrategyField(DimensionFieldKeyEnums.layTypes, ['自愿清算']);
      dimension.strategyFields = [liquidationTypeField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.category23Field.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-liquidation-info');
      expect(mockRiskChangeHelper.category23Field).toHaveBeenCalledWith(
        liquidationTypeField,
        expect.objectContaining({
          Id: 'test-liquidation-info',
          Category: RiskChangeCategoryEnum.category23,
        }),
      );
    });
  });

  describe('严重违法失信 (category28)', () => {
    it('应该正确处理严重违法失信类型的风险动态', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-serious-violation',
        Category: RiskChangeCategoryEnum.category28,
        ChangeExtend: JSON.stringify({
          ViolationType: '严重违法失信',
          ViolationReason: '虚假出资',
          ListDate: '2024-03-10',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const violationTypeField = createTestStrategyField(DimensionFieldKeyEnums.layTypes, ['严重违法失信']);
      dimension.strategyFields = [violationTypeField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.category28Field.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-serious-violation');
      expect(mockRiskChangeHelper.category28Field).toHaveBeenCalledWith(
        violationTypeField,
        expect.objectContaining({
          Id: 'test-serious-violation',
          Category: RiskChangeCategoryEnum.category28,
        }),
      );
    });
  });

  describe('公告及报告 (category113)', () => {
    it('应该正确处理公告及报告类型的风险动态', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-announcement-report',
        Category: RiskChangeCategoryEnum.category113,
        ChangeExtend: JSON.stringify({
          AnnouncementType: '年度报告',
          ReportContent: '财务状况良好',
          PublishDate: '2024-04-01',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const announcementTypeField = createTestStrategyField(DimensionFieldKeyEnums.layTypes, ['年度报告']);
      dimension.strategyFields = [announcementTypeField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.categoryAnnouncementReportField.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-announcement-report');
      expect(mockRiskChangeHelper.categoryAnnouncementReportField).toHaveBeenCalledWith(
        announcementTypeField,
        expect.objectContaining({
          Id: 'test-announcement-report',
          Category: RiskChangeCategoryEnum.category113,
        }),
      );
    });
  });

  describe('股东股份变更 (category114)', () => {
    it('应该正确处理股东股份变更 category114 类型的风险动态', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-shareholder-change-114',
        Category: RiskChangeCategoryEnum.category114,
        ChangeExtend: JSON.stringify({
          ShareChangeStatus: '增持',
          HolderRole: '控股股东',
          BeforeContent: '25%',
          AfterContent: '35%',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const shareChangeStatusField = createTestStrategyField(DimensionFieldKeyEnums.shareChangeStatus, [1]); // 增持
      const holderRoleField = createTestStrategyField(DimensionFieldKeyEnums.holderRole, [1]); // 控股股东
      const beforeContentField = createTestStrategyField(DimensionFieldKeyEnums.beforeContent, [20, 30]);
      const afterContentField = createTestStrategyField(DimensionFieldKeyEnums.afterContent, [30, 40]);
      dimension.strategyFields = [shareChangeStatusField, holderRoleField, beforeContentField, afterContentField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.category114shareChangeStatusField.mockResolvedValue(true);
      mockRiskChangeHelper.category114holderRoleField.mockResolvedValue(true);
      mockRiskChangeHelper.category114beforeContentField.mockReturnValue(true);
      mockRiskChangeHelper.category114afterContentField.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-shareholder-change-114');
      expect(mockRiskChangeHelper.category114shareChangeStatusField).toHaveBeenCalled();
      expect(mockRiskChangeHelper.category114holderRoleField).toHaveBeenCalled();
      expect(mockRiskChangeHelper.category114beforeContentField).toHaveBeenCalled();
      expect(mockRiskChangeHelper.category114afterContentField).toHaveBeenCalled();
    });
  });

  describe('负面舆情 (category134)', () => {
    it('应该正确处理负面舆情类型的风险动态', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-negative-news',
        Category: RiskChangeCategoryEnum.category14, // 使用存在的枚举值
        ChangeExtend: JSON.stringify({
          NewsType: '负面',
          NewsContent: '公司涉嫌财务造假',
          PublishDate: '2024-05-01',
          Source: '财经媒体',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const negativePositiveNewsField = createTestStrategyField(DimensionFieldKeyEnums.inspectionResultType, [0]); // 负面
      dimension.strategyFields = [negativePositiveNewsField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockBaseHelper.hitNegativePositiveNewsField.mockResolvedValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-negative-news');
      expect(mockBaseHelper.hitNegativePositiveNewsField).toHaveBeenCalledWith(
        negativePositiveNewsField,
        expect.objectContaining({
          Id: 'test-negative-news',
          Category: RiskChangeCategoryEnum.category14,
        }),
      );
    });
  });

  describe('变更状态和时间周期阈值计数测试', () => {
    it('应该正确处理带有变更状态字段的风险动态', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-change-status',
        Category: RiskChangeCategoryEnum.category39, // 法定代表人变更
        ChangeExtend: JSON.stringify({
          A: '张三',
          B: '李四',
          ChangeStatus: '已变更',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const changeStatusField = createTestStrategyField(DimensionFieldKeyEnums.changeStatus, ['已变更']);
      dimension.strategyFields = [changeStatusField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.hitChangeStatusField.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-change-status');
      expect(mockRiskChangeHelper.hitChangeStatusField).toHaveBeenCalledWith(
        changeStatusField,
        expect.objectContaining({
          Id: 'test-change-status',
          Category: RiskChangeCategoryEnum.category39,
        }),
      );
    });

    it('应该正确处理带有时间周期阈值计数字段的风险动态', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-time-period-threshold',
        Category: RiskChangeCategoryEnum.category39,
        ChangeExtend: JSON.stringify({
          A: '张三',
          B: '李四',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const timePeriodThresholdCountField = createTestStrategyField(DimensionFieldKeyEnums.layTypes, [{ timePeriod: 6, threshold: 3 }]);
      dimension.strategyFields = [timePeriodThresholdCountField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      // Mock ES搜索返回
      mockSearchEs.mockResolvedValue({
        body: {
          hits: {
            total: { value: 5 },
            hits: [
              { _source: { Id: 'period1', Category: RiskChangeCategoryEnum.category39 } },
              { _source: { Id: 'period2', Category: RiskChangeCategoryEnum.category39 } },
              { _source: { Id: 'period3', Category: RiskChangeCategoryEnum.category39 } },
            ],
          },
        },
      });

      mockRiskChangeHelper.hitTimePeriodThresholdCountField.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-time-period-threshold');
      expect(mockRiskChangeHelper.hitTimePeriodThresholdCountField).toHaveBeenCalledWith(
        timePeriodThresholdCountField,
        expect.objectContaining({
          Id: 'test-time-period-threshold',
          Category: RiskChangeCategoryEnum.category39,
        }),
        expect.any(Object), // periodResponse
      );
    });
  });
});
