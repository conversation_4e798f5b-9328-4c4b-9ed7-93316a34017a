import { Test, TestingModule } from '@nestjs/testing';
import { DimensionHitDetailProcessor } from '../dimension-hit-detail.processor';
import { BaseHelper } from '../helper/base.helper';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { RiskChangeCategoryEnum } from 'libs/enums/riskchange/RiskChangeCategoryEnum';
import { DimensionDefinitionEntity } from 'libs/entities/DimensionDefinitionEntity';
import { DimensionSourceEnums } from 'libs/enums/diligence/DimensionSourceEnums';
import { PersonHelper } from 'apps/data/helper/person.helper';
import { RiskChangeHelper } from 'apps/data/helper/risk.change.helper';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { DimensionHitStrategyFieldsEntity } from 'libs/entities/DimensionHitStrategyFieldsEntity';

// 测试辅助函数
const createTestDimensionStrategy = (key: DimensionTypeEnums): DimensionHitStrategyPO => {
  const def = new DimensionDefinitionEntity();
  def.key = key;
  def.name = `测试维度-${key}`;
  def.source = DimensionSourceEnums.RiskChange;
  return new DimensionHitStrategyPO(def);
};

// 创建测试用的策略字段
const createTestStrategyField = (fieldKey: DimensionFieldKeyEnums, fieldValue: any): DimensionHitStrategyFieldsEntity => {
  const field = new DimensionHitStrategyFieldsEntity();
  field.dimensionFieldKey = fieldKey;
  field.fieldValue = fieldValue;
  return field;
};

describe('DimensionHitDetailProcessor 核心功能测试', () => {
  let processor: DimensionHitDetailProcessor;
  let mockRiskChangeHelper: jest.Mocked<RiskChangeHelper>;
  let mockPersonHelper: jest.Mocked<PersonHelper>;
  let mockBaseHelper: jest.Mocked<BaseHelper>;
  let mockSearchEs: jest.Mock;

  beforeEach(async () => {
    // 创建基础 mock 对象
    mockRiskChangeHelper = {
      hitLayTypesField: jest.fn(),
      hitLayTypesField72: jest.fn(),
      hitIsBPField: jest.fn(),
    } as any;

    mockPersonHelper = {
      getPersonData: jest.fn(),
      getEmployeeData: jest.fn(),
    } as any;

    mockBaseHelper = {
      filterLastYearData: jest.fn(),
      hitNegativePositiveNewsField: jest.fn(),
    } as any;

    mockSearchEs = jest.fn();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DimensionHitDetailProcessor,
        { provide: RiskChangeHelper, useValue: mockRiskChangeHelper },
        { provide: PersonHelper, useValue: mockPersonHelper },
        { provide: BaseHelper, useValue: mockBaseHelper },
      ],
    }).compile();

    processor = module.get<DimensionHitDetailProcessor>(DimensionHitDetailProcessor);
    processor.bindRiskChangeEsSearchFn(mockSearchEs);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('基础功能测试', () => {
    it('应该正确处理空的详情响应', async () => {
      // Arrange
      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toEqual([]);
    });

    it('应该正确处理JSON解析成功的情况', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-json-success',
        Category: RiskChangeCategoryEnum.category139, // 经营地址变更，简单处理
        ChangeExtend: JSON.stringify({
          D: Math.floor(Date.now() / 1000),
        }),
        Extend1: JSON.stringify({
          someData: 'test',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockBaseHelper.filterLastYearData.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-json-success');
      // 验证内部处理时JSON被正确解析
      expect(mockBaseHelper.filterLastYearData).toHaveBeenCalledWith(
        expect.objectContaining({
          Id: 'test-json-success',
          Category: RiskChangeCategoryEnum.category139,
          ChangeExtend: {
            D: expect.any(Number),
          },
          Extend1: {
            someData: 'test',
          },
        }),
      );
    });

    it('应该正确处理JSON解析错误的情况', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-json-error',
        Category: RiskChangeCategoryEnum.category139,
        ChangeExtend: 'invalid-json', // 无效的JSON
        Extend1: 'also-invalid-json',
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockBaseHelper.filterLastYearData.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-json-error');
      // JSON解析失败时，应该保持原值
      expect(result[0].ChangeExtend).toBe('invalid-json');
      expect(result[0].Extend1).toBe('also-invalid-json');
    });

    it('应该正确处理null和undefined的JSON字段', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-null-json',
        Category: RiskChangeCategoryEnum.category139,
        ChangeExtend: null,
        Extend1: undefined,
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockBaseHelper.filterLastYearData.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(mockBaseHelper.filterLastYearData).toHaveBeenCalledWith(
        expect.objectContaining({
          ChangeExtend: {},
          Extend1: {},
        }),
      );
    });

    it('应该正确处理异常情况', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-exception',
        Category: RiskChangeCategoryEnum.category139,
        ChangeExtend: JSON.stringify({
          D: Math.floor(Date.now() / 1000),
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      // Mock BaseHelper 方法抛出异常
      mockBaseHelper.filterLastYearData.mockImplementation(() => {
        throw new Error('测试异常');
      });

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      // 异常情况下，该项不会被添加到结果中
      expect(result).toHaveLength(0);
      expect(mockBaseHelper.filterLastYearData).toHaveBeenCalled();
    });

    it('应该正确处理不命中的情况', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-no-hit',
        Category: RiskChangeCategoryEnum.category139,
        ChangeExtend: JSON.stringify({
          D: Math.floor(Date.now() / 1000),
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      // Mock BaseHelper 方法返回 false（不命中）
      mockBaseHelper.filterLastYearData.mockReturnValue(false);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(0); // 不命中，应该返回空数组
      expect(mockBaseHelper.filterLastYearData).toHaveBeenCalled();
    });
  });

  describe('bindRiskChangeEsSearchFn 方法测试', () => {
    it('应该正确绑定ES搜索函数', () => {
      // Arrange
      const mockSearchFn = jest.fn();

      // Act
      processor.bindRiskChangeEsSearchFn(mockSearchFn);

      // Assert
      expect(processor.searchEs).toBe(mockSearchFn);
    });
  });

  describe('getDimesionTopics 方法测试', () => {
    it('应该正确获取维度主题', () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const topicsField = createTestStrategyField(DimensionFieldKeyEnums.topics, ['主题1', '主题2']);
      dimension.strategyFields = [topicsField];

      // Act
      const result = (processor as any).getDimesionTopics(dimension);

      // Assert
      expect(result).toEqual(['主题1', '主题2']);
    });

    it('应该在没有主题字段时返回null', () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      dimension.strategyFields = [];

      // Act
      const result = (processor as any).getDimesionTopics(dimension);

      // Assert
      expect(result).toEqual([]);
    });
  });

  describe('commonCivilRiskChange 方法测试', () => {
    it('应该正确调用ES搜索并返回结果', async () => {
      // Arrange
      const companyIds = ['test-company-id'];
      const categories = [RiskChangeCategoryEnum.category37];
      const periodTime = 1;
      const periodUnit = 'year';

      const mockEsResponse = {
        body: {
          hits: {
            total: { value: 5 },
            hits: [
              { _source: { Id: 'result1', Category: RiskChangeCategoryEnum.category37 } },
              { _source: { Id: 'result2', Category: RiskChangeCategoryEnum.category37 } },
            ],
          },
        },
      };

      mockSearchEs.mockResolvedValue(mockEsResponse);

      // Act
      const result = await processor.commonCivilRiskChange(companyIds, categories, periodTime, periodUnit);

      // Assert
      expect(result.Paging.TotalRecords).toBe(5);
      expect(result.Result).toHaveLength(2);
      expect(result.Result[0].Id).toBe('result1');
      expect(result.Result[1].Id).toBe('result2');
      expect(mockSearchEs).toHaveBeenCalledWith(expect.any(Object), companyIds[0]);
    });

    it('应该正确处理ES搜索异常', async () => {
      // Arrange
      const companyIds = ['test-company-id'];
      const categories = [RiskChangeCategoryEnum.category37];
      const periodTime = 1;
      const periodUnit = 'year';

      const mockError = new Error('ES搜索失败');
      mockSearchEs.mockRejectedValue(mockError);

      // Act & Assert
      await expect(processor.commonCivilRiskChange(companyIds, categories, periodTime, periodUnit)).rejects.toThrow('ES搜索失败');
    });

    it('应该正确处理空的ES搜索结果', async () => {
      // Arrange
      const companyIds = ['test-company-id'];
      const categories = [RiskChangeCategoryEnum.category37];
      const periodTime = 1;
      const periodUnit = 'year';

      const mockEsResponse = {
        body: {
          hits: {
            total: { value: 0 },
            hits: [],
          },
        },
      };

      mockSearchEs.mockResolvedValue(mockEsResponse);

      // Act
      const result = await processor.commonCivilRiskChange(companyIds, categories, periodTime, periodUnit);

      // Assert
      expect(result.Paging.TotalRecords).toBe(0);
      expect(result.Result).toHaveLength(0);
    });

    it('应该正确处理多个公司ID的情况', async () => {
      // Arrange
      const companyIds = ['company1', 'company2', 'company3'];
      const categories = [RiskChangeCategoryEnum.category37, RiskChangeCategoryEnum.category39];
      const periodTime = 6;
      const periodUnit = 'month';

      const mockEsResponse = {
        body: {
          hits: {
            total: { value: 10 },
            hits: [
              { _source: { Id: 'result1', Category: RiskChangeCategoryEnum.category37 } },
              { _source: { Id: 'result2', Category: RiskChangeCategoryEnum.category39 } },
            ],
          },
        },
      };

      mockSearchEs.mockResolvedValue(mockEsResponse);

      // Act
      const result = await processor.commonCivilRiskChange(companyIds, categories, periodTime, periodUnit);

      // Assert
      expect(result.Paging.TotalRecords).toBe(10);
      expect(result.Result).toHaveLength(2);
      expect(mockSearchEs).toHaveBeenCalledWith(
        expect.objectContaining({
          query: expect.objectContaining({
            bool: expect.objectContaining({
              filter: expect.arrayContaining([
                expect.objectContaining({
                  terms: expect.objectContaining({
                    KeyNo: companyIds,
                  }),
                }),
                expect.objectContaining({
                  terms: expect.objectContaining({
                    Category: categories,
                  }),
                }),
              ]),
            }),
          }),
        }),
        companyIds[0],
      );
    });
  });
});
