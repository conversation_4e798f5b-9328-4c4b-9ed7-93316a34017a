import { Injectable } from '@nestjs/common';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { Client } from '@elastic/elasticsearch';
import { ConfigService } from 'libs/config/config.service';
import { CreditAggBucketItemPO } from 'libs/model/data/source/credit.analyze/CreditAggBucketItemPO';
import { RedisService } from '@kezhaozhao/nestjs-redis';
import { attempt, difference, find, flatten, isError, isNumber } from 'lodash';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { creditSearchSourceMappings } from 'libs/constants/credit.analyze.constants';
import { toRoundFixed, transferToNumber } from 'libs/utils/utils';
import { getCreditMappingByDimension } from 'libs/utils/diligence/credit.utils';
import { BaseEsAnalyzeService } from './base-es-analyze.service';
import { DimensionHitResultPO } from '../../../libs/model/diligence/dimension/DimensionHitResultPO';
import * as Bluebird from 'bluebird';
import { HitDetailsBaseResponse } from '../../../libs/model/diligence/details/response';
import * as moment from 'moment';
import { DATE_FORMAT, HistoryValidNumbersArr, NoLimitValidNumbersArr } from '../../../libs/constants/common';
import { CompanySearchService } from '../../company/company-search.service';
import { HitDetailsBaseQueryParams, HitDetailsCreditParam } from '../../../libs/model/diligence/details/request';
import { DimensionAnalyzeParamsPO } from '../../../libs/model/data/source/DimensionAnalyzeParamsPO';
import { DimensionFieldKeyEnums } from '../../../libs/enums/dimension/dimension.filter.params';
import { DimensionFieldCompareTypeEnums, EsOperator } from '../../../libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { processDimHitResPO } from '../../../libs/utils/diligence/dimension.utils';
import { getCompareResult, getStartTimeByCycle } from '../../../libs/utils/diligence/diligence.utils';
import { BusinessAbnormalType, PenaltiesType } from '../../../libs/constants/punish.constants';
import { TargetInvestigationEnums } from '../../../libs/enums/dimension/FieldValueEnums';
import { CompanyDetailService } from '../../company/company-detail.service';
import { DateDetailsQueryParams } from '../../../libs/model/data/request/HitDetailsBaseQueryParams';
import { PersonHelper } from '../helper/person.helper';

@Injectable()
export class CreditEsSource extends BaseEsAnalyzeService {
  private expectedDimensionsTypes: DimensionTypeEnums[] = flatten(creditSearchSourceMappings.map((t) => t.dimensionType));

  constructor(
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
    private readonly personHelper: PersonHelper,
    private readonly companyService: CompanySearchService,
    private readonly companyDetailsService: CompanyDetailService,
  ) {
    super(
      CreditEsSource.name,
      new Client({
        nodes: configService.esConfig.credit.nodes,
        ssl: { rejectUnauthorized: false },
      }),
      configService.esConfig.credit.indexName,
    );
  }

  async analyze(companyId: string, dimensionHitStrategyPOs: DimensionHitStrategyPO[], params?: DimensionAnalyzeParamsPO): Promise<DimensionHitResultPO[]> {
    try {
      if (!companyId || !dimensionHitStrategyPOs?.length) {
        return [];
      }
      const diffTypes = difference(
        dimensionHitStrategyPOs.map((m) => m.key),
        this.expectedDimensionsTypes,
      );
      if (diffTypes?.length) {
        this.logger.warn('passed dimension contains not expected ones, will remove unexpected keys');
        dimensionHitStrategyPOs = dimensionHitStrategyPOs.filter((po) => this.expectedDimensionsTypes.some((t) => t == po.key));
      }
      const result: DimensionHitResultPO[] = [];

      await Bluebird.map(dimensionHitStrategyPOs, async (po) => {
        const hitStrategy = await super.analyze(companyId, [po], params);
        result.push(...hitStrategy);
      });

      return result;
    } catch (e) {
      this.logger.error(e);
      throw e;
    }
  }

  async getDimensionDetail(
    dimension: DimensionHitStrategyPO,
    params: HitDetailsCreditParam,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    const { keyNo } = params;
    if (dimension.key === DimensionTypeEnums.BusinessAbnormal5) {
      const company = await this.companyService.companyDetailsQcc(keyNo);
      if (['停业', '撤销', '注销', '吊销', '歇业', '责令关闭'].includes(company?.ShortStatus) || company?.EconKind === '个体工商户') {
        return HitDetailsBaseResponse.ok();
      }
    }
    const resp: HitDetailsBaseResponse = await super.getDimensionDetail(dimension, params, analyzeParams);
    return resp;
  }

  /**
   * 有的返回结果也调用接口去补充数据
   * 对DimensionDetail返回结果的处理，如果是isScanRisk,则跳出返回结果的处理
   * @param resp
   * @param dimension
   * @param params
   * @param analyzeParams
   * @protected
   */
  protected async getDimensionDetailItemData(
    resp: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    if (analyzeParams?.isScanRisk) {
      return resp;
    }
    if (resp?.Result?.length) {
      this.formatResponse(resp);
      switch (dimension?.key) {
        // 主要人员限制高消费
        case DimensionTypeEnums.MainMembersRestrictedConsumptionCurrent:
        case DimensionTypeEnums.RestrictedConsumptionHistory:
        case DimensionTypeEnums.MainMembersPersonCreditCurrent:
        case DimensionTypeEnums.RestrictedConsumptionCurrent: {
          // 主要人员限制高消费 不返回企业主体标签
          const { personJobSet } = await this.personHelper.getCompanyExecutivesKeyNosV2(params.keyNo, 'person', true, true);
          resp?.Result?.forEach((x) => {
            // 限消令对象
            x['SubjectInfo'] = x.NameAndKeyNo.filter((a) => a.KeyNo == x.KeyNo);
            x?.SubjectInfo?.forEach((p) => {
              p['Job'] = personJobSet[p?.KeyNo];
            });
            // 限消令关联对象
            x['PledgorInfo'] = x.NameAndKeyNo.filter((a) => x?.p_pledgor_id?.includes(a.KeyNo) || x?.pledgor_id?.includes(a.KeyNo));
            x?.PledgorInfo?.forEach((p) => {
              p['Job'] = personJobSet[p?.KeyNo];
            });
            Object.assign(x, {
              FileUrl: x?.osskey?.length > 0 ? `https://qccdata.qichacha.com/CountXG/${x.osskey}` : '',
              // 申请人
              ApplicantInfo: x.Specs,
            });
          });
          break;
        }
        case DimensionTypeEnums.Bankruptcy: {
          resp?.Result?.forEach((x) => {
            // 被申请人
            x['SubjectInfo'] = x.NameAndKeyNo.filter((a) => a.KeyNo == x.KeyNo);
          });
          break;
        }
        default:
          break;
      }
    }
    return resp;
  }

  private formatResponse(resp: HitDetailsBaseResponse) {
    resp.Result.forEach((source) => {
      const Applicant = source.applicant || source.executionapplicant;
      const ApplicantInfo = Applicant ? JSON.parse(source?.nameandkeyno)?.filter((sn) => Applicant?.includes(sn?.Name)) : JSON.parse(source?.nameandkeyno);
      const NameAndKeyNo = source.nameandkeyno ? JSON.parse(source.nameandkeyno) : undefined;

      const Specs = source.specs ? (isError(attempt(JSON.parse, source.specs)) ? source.specs : attempt(JSON.parse, source.specs)) : undefined;
      if (source.osskey?.length > 0 && source.osskey.includes(',')) {
        source.osskey = source.osskey.split(',')[0];
      }
      Object.assign(source, {
        Id: source.id,
        KeyNo: source.keyno,
        Title: source.title,
        Name: source.name || source.pername,
        Type: source.type,
        RiskId: source.riskid,
        DataStatus: source.datastatus,
        HasImage: source.hasimage,
        StockInfo: source.stockinfo,
        Product: source.product,
        PunishGovCode: source.punishgovcode,
        OssId: source.ossid,
        Province: source.province,
        AreaCode: source.areacode,
        ProvinceDesc: source.provincedesc,
        TypeDesc: source.typedesc,
        IdNum: source.idnum,
        CreditCode: source.creditcode,
        CompOrg: source.comporg,
        OssKey: source.osskey,
        Applicant,
        Pledgor: source.pledgor,
        TagJsonArray: source.tagjsonarray ? JSON.parse(source.tagjsonarray) : undefined,
        NameAndKeyNo,
        CaseReasonType: source.casereasontype || source.removereason,
        PublishTypeDesc: source.publishtypedesc,
        CaseReason: source.casereason,
        PublishDate: source.publishdate,
        CurrenceDate: source.occurrencedate,
        OccurrenceDate: source.occurrencedate,
        Court: source.court,
        OrgNo: source.orgno,
        RemoveReason: source.removereason,
        OriginalName: source.originalname,
        PunishDate: source.punishdate,
        CourtCode: source.courtcode,
        CourtProvince: source.courtprovince,
        CaseNo: source.caseno,
        Amount: source.amount,
        ActionRemark: source.actionremark || source.removereason,
        FileUrl: source?.osskey?.length > 0 ? `https://qccdata.qichacha.com/CaseSupervisePunish/${source.osskey}` : '',
        ExecutionApplicant: source.executionapplicant,
        LianDate: source.liandate,
        PPledgor: source.p_pledgor,
        Specs,
        ApplicantInfo,
        AmountDesc: source.amountdesc,
        ExecuteStatus: source.executestatus,
        Address: source.address,
        docNo: source.caseno,
        punishReason: source.casereasonclean,
        punishResult: source.title,
        punishOffice: source.court,
        punishDate: moment(source?.punishdate * 1000).format(DATE_FORMAT),
        Amount2: source?.amount2,
      });
      if (source.NameAndKeyNo) {
        if (!(source.NameAndKeyNo instanceof Array)) {
          source.NameAndKeyNo = [source.NameAndKeyNo];
        }
      }
    });
  }

  protected async createAggs(companyId: string, DimensionHitStrategyPOs: DimensionHitStrategyPO[], params: HitDetailsCreditParam) {
    const aggs: any = {};
    await Bluebird?.map(DimensionHitStrategyPOs, async (po) => {
      const dimQuery = await this.getDimensionQuery(companyId, po, params);
      if (dimQuery) {
        const aggsName = `${this.bucketNamePrefix}${po.strategyId}`;
        aggs[aggsName] = {
          filter: dimQuery,
          aggs: {
            amount: {
              sum: {
                field: 'amount',
              },
            },
            amount2: {
              sum: {
                field: 'amount2',
              },
            },
          },
        };
        // switch (po.key) {
        //   case DimensionTypeEnums.HitOuterBlackList: {
        //     // const blacklistSettingItems = po.subDimensionList.filter((sub) => sub.status > 0);
        //     // // 需要按照风险等级分别统计，维度的风险等级取最高的那个
        //     // Object.keys(DimensionRiskLevelEnum)
        //     //   .filter((level) => isNaN(Number(level)))
        //     //   .forEach((level) => {
        //     //     const outerBlackPublishTypesLevel = flatten(
        //     //       blacklistSettingItems
        //     //         .filter((item) => item.strategyModel.level == DimensionRiskLevelEnum[level] && !!item.child)
        //     //         .map((item) => item.child.map((s) => s - 15 * 100)),
        //     //     );
        //     //     if (outerBlackPublishTypesLevel.length > 0) {
        //     //       aggs[aggsName].aggs[`agg_${level}`] = { filter: { terms: { publishtype: outerBlackPublishTypesLevel } } };
        //     //     }
        //     //   });
        //     break;
        //   }
        // }
      }
    });
    return aggs;
  }

  protected processAggs(aggObj: any): CreditAggBucketItemPO[] {
    const bucketData: CreditAggBucketItemPO[] = [];
    Object.keys(aggObj).forEach((bucketName) => {
      // const bucket = aggObj[bucketName];
      // const dimensionType = bucketName.replace(this.bucketNamePrefix, '') as DimensionTypeEnums;
      const dimensionType = bucketName.replace(this.bucketNamePrefix, '');
      const bucket = aggObj[bucketName];
      // const esMappingModel: CreditEsMappingModel = getCreditMappingByDimension(dimensionType);
      // if (esMappingModel) {
      const hitCount = bucket['doc_count'];
      if (hitCount > 0) {
        const res: CreditAggBucketItemPO = {
          dimensionType,
          hitCount,
        };
        if (bucket.amount) {
          res.amount = transferToNumber(bucket.amount.value);
        }
        if (bucket.amount2) {
          res.amount2 = transferToNumber(bucket.amount2.value);
        }
        bucketData.push(res);
      }
      // }
    });
    return bucketData;
  }

  protected processBucketData(bucketData: CreditAggBucketItemPO[], DimensionHitStrategyPOs: DimensionHitStrategyPO[]): DimensionHitResultPO[] {
    if (bucketData.length > 0) {
      return bucketData
        .map((item) => {
          const d: DimensionHitStrategyPO = find(DimensionHitStrategyPOs, { strategyId: +item.dimensionType });
          const desData = {
            isHidden: '',
            isHiddenY: '',
          };
          const { hitCount, amount, amount2 } = item as CreditAggBucketItemPO;

          switch (d.key) {
            case DimensionTypeEnums.BondDefaults: {
              //  债券违约
              if (amount) {
                Object.assign(desData, { amountY: toRoundFixed(amount / 10000 / 10000, 2) });
              }
              if (amount2) {
                Object.assign(desData, { amount2Y: toRoundFixed(amount2 / 10000 / 10000, 2) });
                //维度未升级 cover
                Object.assign(desData, { amountW: amount2 / 10000 });
              }
              break;
            }
            case DimensionTypeEnums.EnvironmentalPenalties: // '环保处罚',  countInfo.ENPCountV2
            case DimensionTypeEnums.BusinessAbnormal3: // 被列入经营异常名录
            case DimensionTypeEnums.Bankruptcy: // '破产重整', countInfo.BankruptcyCount
            case DimensionTypeEnums.MainMembersRestrictedConsumptionCurrent: // 主要人员限制高消费
            case DimensionTypeEnums.MainMembersRestrictedOutbound: // 主要人员限制出境
            case DimensionTypeEnums.OperationAbnormal: // '多次被列入经营异常名录【当下未列入】'
            // case DimensionTypeEnums.AdministrativePenalties: // '行政处罚',元
            case DimensionTypeEnums.MainMembersPersonCreditCurrent: //主要人员被列入失信被执行人，元
            case DimensionTypeEnums.PersonExecution:
            case DimensionTypeEnums.PersonCreditCurrent:
            case DimensionTypeEnums.PersonCreditHistory: // '被列入失信被执行人（历史）'
            case DimensionTypeEnums.FreezeEquity: {
              // '股权冻结', 万元
              if (amount) {
                Object.assign(desData, { amountW: toRoundFixed(amount / 10000, 2) });
              }
              break;
            }
          }

          let hit = true;
          // 命中记录条数 规则设置
          const hitCountField = d.getStrategyFieldByKey(DimensionFieldKeyEnums.hitCount);
          if (hitCountField && !getCompareResult(hitCount, hitCountField.fieldValue[0], hitCountField.compareType)) {
            // 不满足 命中记录条数规则 标记未命中
            hit = false;
          }

          if (hit) {
            return processDimHitResPO(d, hitCount, desData);
          }
          return null;
        })
        .filter((t) => t);
    } else {
      return DimensionHitStrategyPOs.map((d) => {
        const isShowTipField = d.getStrategyFieldByKey(DimensionFieldKeyEnums.isShowTip);
        if (isShowTipField && isShowTipField.fieldValue[0] === 1) {
          return processDimHitResPO(d, 0, {
            isHidden: '',
            isHiddenY: '',
          });
        }
        return null;
      }).filter(Boolean);
    }
  }

  protected async getDimensionQuery(companyId: string, dimension: DimensionHitStrategyPO, params: HitDetailsCreditParam): Promise<object> {
    const subBool = {
      bool: {
        must: [],
        should: [],
      },
    };
    const esMappingModel = getCreditMappingByDimension(dimension.key);
    if (esMappingModel) {
      subBool.bool.must.push({
        term: {
          type: esMappingModel.type,
        },
      });

      let useKeyNo = true;
      let datastatus = NoLimitValidNumbersArr;
      let ids = [companyId];

      const cycleField = dimension.getCycleField();
      const cycle = cycleField ? (cycleField.fieldValue?.[0] as number) : 0;
      const operator = cycleField ? EsOperator[cycleField.compareType] : 'gte';
      let rangField = 'publishdate';
      switch (dimension.key) {
        case DimensionTypeEnums.MainMembersPersonCreditCurrent:
        case DimensionTypeEnums.MainMembersRestrictedConsumptionCurrent: {
          datastatus = NoLimitValidNumbersArr;
          const { personNos } = await this.personHelper.getCompanyExecutivesKeyNosV2(companyId, 'person', true, true);
          ids = personNos?.length ? personNos : [];
          if (ids.length) {
            subBool.bool.should.push({ terms: { keyno: ids } }, { terms: { p_pledgor_id: ids } }, { terms: { pledgor_id: ids } });
            subBool.bool['minimum_should_match'] = 1;
          }
          useKeyNo = false;
          break;
        }
        case DimensionTypeEnums.MainMembersRestrictedOutbound: {
          //  排查对象
          const fieldValue = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.targetInvestigation)?.fieldValue?.[0] ?? TargetInvestigationEnums.Self;
          const perKeyNos: string[] = [];
          switch (fieldValue) {
            case TargetInvestigationEnums.Legal: {
              const companyDetail = await this.companyService.companyDetailsQcc(companyId);
              if (companyDetail?.Oper?.KeyNo) {
                perKeyNos.push(companyDetail.Oper.KeyNo);
              }
              break;
            }
            case TargetInvestigationEnums.HisLegal: {
              const res = await this.companyDetailsService.getCoyHistoryInfo(companyId);
              if (res?.Result?.OperList?.length) {
                const keyNos: string[] = res?.Result?.OperList?.filter((t) => !!t.KeyNo).map((Oper) => Oper.KeyNo);
                perKeyNos.push(...keyNos);
              }
              break;
            }
            case TargetInvestigationEnums.Self: {
              perKeyNos.push(companyId);
              break;
            }
          }
          if (!perKeyNos?.length) {
            perKeyNos.push('-1');
          }
          if (perKeyNos.length) {
            subBool.bool.should.push({ terms: { keyno: perKeyNos } }, { terms: { p_pledgor_id: perKeyNos } }, { terms: { pledgor_id: perKeyNos } });
            subBool.bool['minimum_should_match'] = 1;
          }
          useKeyNo = false;
          break;
        }
        case DimensionTypeEnums.OperationAbnormal: {
          datastatus = [0, 10, 11, 13, 91, 93];
          break;
        }
        case DimensionTypeEnums.RestrictedConsumptionCurrent: {
          //  排查对象
          const fieldValue = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.targetInvestigation)?.fieldValue?.[0] ?? TargetInvestigationEnums.Self;
          const perKeyNos: string[] = [];
          switch (fieldValue) {
            case TargetInvestigationEnums.Legal: {
              const companyDetail = await this.companyService.companyDetailsQcc(companyId);
              if (companyDetail?.Oper?.KeyNo) {
                perKeyNos.push(companyDetail.Oper.KeyNo);
              }
              break;
            }
            case TargetInvestigationEnums.HisLegal: {
              const res = await this.companyDetailsService.getCoyHistoryInfo(companyId);
              if (res?.Result?.OperList?.length) {
                const keyNos: string[] = res?.Result?.OperList?.filter((t) => !!t.KeyNo)?.map((Oper) => Oper.KeyNo);
                if (keyNos?.length) {
                  perKeyNos.push(...keyNos);
                }
              }
              break;
            }
            case TargetInvestigationEnums.Self: {
              perKeyNos.push(companyId);
              break;
            }
            case TargetInvestigationEnums.ActualController: {
              const personData = await this.personHelper.getFinalActualController(companyId);
              const keyNos = personData?.filter((p) => !!p.keyNo)?.map((p) => p.keyNo);
              if (keyNos?.length) {
                perKeyNos.push(...keyNos);
              }
              break;
            }
            case TargetInvestigationEnums.LargestShareholder: {
              const partnerData = await this.personHelper.getPartnerList(companyId, 'all');
              const keyNos = partnerData?.filter((p) => !!p.keyNo && p.tags?.includes('大股东')).map((p) => p.keyNo);
              if (keyNos?.length) {
                perKeyNos.push(...keyNos);
              }
              break;
            }
          }
          if (!perKeyNos?.length) {
            perKeyNos.push('-1');
          }
          if (perKeyNos?.length) {
            subBool.bool.should.push({ terms: { keyno: perKeyNos } }, { terms: { p_pledgor_id: perKeyNos } }, { terms: { pledgor_id: perKeyNos } });
            subBool.bool['minimum_should_match'] = 1;
          }
          datastatus = [1];
          useKeyNo = false;
          break;
        }
        case DimensionTypeEnums.BusinessAbnormal3: // 被列入经营异常名录 ,根据publishtype字段，还可以分为多个子类
        case DimensionTypeEnums.CancellationOfFiling: // 注销备案
        case DimensionTypeEnums.PersonCreditCurrent: {
          datastatus = [1];
          break;
        }
        case DimensionTypeEnums.PersonCreditHistory: {
          datastatus = [0];
          break;
        }
        case DimensionTypeEnums.RestrictedConsumptionHistory: {
          //  排查对象
          const fieldValue = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.targetInvestigation)?.fieldValue?.[0] ?? TargetInvestigationEnums.Self;
          const perKeyNos: string[] = [];
          switch (fieldValue) {
            case TargetInvestigationEnums.Legal: {
              const companyDetail = await this.companyService.companyDetailsQcc(companyId);
              if (companyDetail?.Oper?.KeyNo) {
                perKeyNos.push(companyDetail.Oper.KeyNo);
              }
              break;
            }
            case TargetInvestigationEnums.HisLegal: {
              const res = await this.companyDetailsService.getCoyHistoryInfo(companyId);
              if (res?.Result?.OperList?.length) {
                const keyNos: string[] = res?.Result?.OperList?.filter((t) => !!t.KeyNo)?.map((Oper) => Oper.KeyNo);
                if (keyNos?.length) {
                  perKeyNos.push(...keyNos);
                }
              }
              break;
            }
            case TargetInvestigationEnums.Self: {
              perKeyNos.push(companyId);
              break;
            }
          }
          if (!perKeyNos?.length) {
            perKeyNos.push('-1');
          }
          if (perKeyNos?.length) {
            subBool.bool.should.push({ terms: { keyno: perKeyNos } }, { terms: { p_pledgor_id: perKeyNos } }, { terms: { pledgor_id: perKeyNos } });
            subBool.bool['minimum_should_match'] = 1;
          }
          datastatus = HistoryValidNumbersArr;
          useKeyNo = false;
          break;
        }
        // 股权冻结
        case DimensionTypeEnums.FreezeEquity: {
          // subBool.bool.must.push({
          //   multi_match: {
          //     fields: ['name', 'pledgor'],
          //     query: companyName,
          //     type: 'phrase',
          //   },
          // });
          datastatus = NoLimitValidNumbersArr;
          // 股权冻结查询 被执行和或者出质人 是当前企业的
          if (ids?.length) {
            subBool.bool.should.push({ terms: { keyno: ids } }, { terms: { pledgor_id: ids } });
            subBool.bool['minimum_should_match'] = 1;
          }
          useKeyNo = false;
          break;
        }
        case DimensionTypeEnums.AdministrativePenalties3: {
          //该维度需求为只查三年前的数据
          const r = getStartTimeByCycle(3);
          subBool.bool.must.push({ range: { occurrencedate: { lte: Math.ceil(r / 1000) } } });
          subBool.bool.should.push(
            { match_phrase: { casereason: '商业贿赂' } },
            { match_phrase: { casereason: '垄断' } },
            { match_phrase: { casereason: '政府采购违法行为' } },
          );
          subBool.bool['minimum_should_match'] = 1;
          datastatus = NoLimitValidNumbersArr;
          break;
        }
        case DimensionTypeEnums.AdministrativePenalties2: {
          subBool.bool.should.push(
            { match_phrase: { casereason: '商业贿赂' } },
            { match_phrase: { casereason: '垄断' } },
            { match_phrase: { casereason: '政府采购违法行为' } },
          );
          subBool.bool['minimum_should_match'] = 1;
          datastatus = NoLimitValidNumbersArr;
          rangField = 'punishdate';
          break;
        }
        case DimensionTypeEnums.EnvironmentalPenalties:
          datastatus = NoLimitValidNumbersArr;
          rangField = 'punishdate';
          break;
        case DimensionTypeEnums.RegulateFinance: {
          rangField = 'punishdate';
          break;
        }
        case DimensionTypeEnums.BusinessAbnormal5: {
          subBool.bool.must.push({
            range: { punishdate: { lte: Math.ceil(Date.now() / 1000) } },
          });
          //增加时间条件为近6个月
          subBool.bool.must.push({
            range: { punishdate: { gte: Math.ceil(getStartTimeByCycle(0.5) / 1000) } },
          });
          const titleMatch = ['停业', '责令停产', '停产', '暂扣', '吊销', '吊销%许可证', '吊销%营业执照'];
          titleMatch.forEach((t) => subBool.bool.should.push({ match_phrase: { title: t } }));
          subBool.bool['minimum_should_match'] = 1;
          datastatus = [1];
          break;
        }
        case DimensionTypeEnums.TaxationOffences:
        case DimensionTypeEnums.SpotCheck:
        case DimensionTypeEnums.ProductQualityProblem1:
        case DimensionTypeEnums.PersonExecution:
        case DimensionTypeEnums.ContractBreach:
        case DimensionTypeEnums.ProductQualityProblem2:
        case DimensionTypeEnums.ProductQualityProblem6:
        case DimensionTypeEnums.ProductQualityProblem7:
        case DimensionTypeEnums.ProductQualityProblem8: {
          break;
        }
        case DimensionTypeEnums.Bankruptcy: {
          datastatus = NoLimitValidNumbersArr;
          break;
        }
        case DimensionTypeEnums.BondDefaults: {
          // const validField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isValid);
          // let isValid = validField?.fieldValue[0];
          const isValid = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isValid)?.fieldValue?.[0] ?? -1;
          // const isValid = strategyModel?.detailsParams?.find((po) => po.field === DimensionFieldKeyEnums.isValid)?.fieldVal || -1;
          let executestatus = [1, 2]; //当前有效，过滤掉已兑付数据
          if (Number(isValid) === -1) {
            // 不限，所有状态数据
            executestatus = [1, 2, 3];
          }
          subBool.bool.must.push({ terms: { executestatus } });
          break;
        }
      }
      if (dimension.strategyFields.length) {
        dimension.strategyFields.forEach((strategyField) => {
          // const field: DimensionFieldKeyEnums = queryPo.field; // publishdate,currencedate,isValid,liandate,amount 等？
          const fieldKey = strategyField.dimensionFieldKey as DimensionFieldKeyEnums;

          // const field = dimension.getStrategyFieldByKey(fieldKey);
          switch (fieldKey) {
            case DimensionFieldKeyEnums.isValid:
              if (Number(strategyField?.fieldValue[0]) >= 0) {
                datastatus = [Number(strategyField?.fieldValue[0])];
              }
              break;
            case DimensionFieldKeyEnums.penaltiesAmount: //处罚金额
            case DimensionFieldKeyEnums.equityAmount: //股权数额
            case DimensionFieldKeyEnums.taxArrearsAmount: //欠税金额
            case DimensionFieldKeyEnums.registrationAmount: //注册金额
              //金额筛选条件
              if (isNumber(strategyField?.fieldValue[0])) {
                // 设置的单位是万元换算成元
                const fieldVal = Number(strategyField?.fieldValue[0]) * 10000;
                const query: any = {};
                switch (strategyField.compareType) {
                  case DimensionFieldCompareTypeEnums.Equal: {
                    Object.assign(query, { term: { amount: fieldVal } });
                    break;
                  }
                  case DimensionFieldCompareTypeEnums.GreaterThanOrEqual: {
                    subBool.bool.must.push({
                      range: {
                        amount: {
                          gte: fieldVal,
                        },
                      },
                    });
                    break;
                  }
                  case DimensionFieldCompareTypeEnums.LessThanOrEqual: {
                    subBool.bool.must.push({
                      range: {
                        amount: {
                          lte: fieldVal,
                        },
                      },
                    });
                    break;
                  }
                  case DimensionFieldCompareTypeEnums.GreaterThan: {
                    subBool.bool.must.push({
                      range: {
                        amount: {
                          gt: fieldVal,
                        },
                      },
                    });
                    break;
                  }
                  case DimensionFieldCompareTypeEnums.LessThan: {
                    subBool.bool.must.push({
                      range: {
                        amount: {
                          lt: fieldVal,
                        },
                      },
                    });
                    break;
                  }
                }
              }
              break;

            case DimensionFieldKeyEnums.executionTarget: //执行标的
            case DimensionFieldKeyEnums.capitalReduction: //资本降幅
            case DimensionFieldKeyEnums.changeThreshold: //变更阈值
            case DimensionFieldKeyEnums.duration: //成立时长
              break;
            case DimensionFieldKeyEnums.penaltiesType: {
              //处罚类型
              const publishtypeItems: string[] = strategyField?.fieldValue?.map((v) => PenaltiesType.find((t) => t.value == v)?.esCode || '').filter((t) => t);
              if (publishtypeItems?.length > 0) {
                subBool.bool.must.push({
                  terms: {
                    publishtype: publishtypeItems, // 'A001', 'A002'
                  },
                });
              }

              break;
            }
            case DimensionFieldKeyEnums.businessAbnormalType: {
              //经营异常类型
              const publishtypeItems: string[] = strategyField?.fieldValue
                ?.map((v) => BusinessAbnormalType.find((t) => t.value == v)?.esCode || '')
                .filter((t) => t);
              if (publishtypeItems?.length > 0)
                subBool.bool.must.push({
                  terms: {
                    publishtype: publishtypeItems,
                  },
                });

              break;
            }
          }
        });
      }
      if (cycle > 0) {
        const timestamp = getStartTimeByCycle(cycle);
        const range = {};
        range[rangField] = { [operator]: Math.ceil(timestamp / 1000) };
        subBool.bool.must.push({ range });
      }
      subBool.bool.must.push({
        terms: {
          datastatus,
        },
      });
      if (useKeyNo && ids?.length) {
        subBool.bool.must.push({
          terms: { keyno: ids },
        });
      }
      return subBool;
    }
    return null;
  }

  /**
   * 根据riskid查询具体维度数据
   * @param params
   * @returns
   */
  async getDateByIds(dimensionKey: DimensionTypeEnums, params: DateDetailsQueryParams): Promise<HitDetailsBaseResponse> {
    if (!params.ids?.length) {
      return HitDetailsBaseResponse.ok();
    }
    try {
      const pageIndex = params?.pageIndex || 1;
      const pageSize = params?.pageSize || 5;
      const sortField = params?.sortField || 'publishdate';
      const sortOrder = params?.sortOrder || 'DESC';

      const esMappingModel = getCreditMappingByDimension(dimensionKey);
      if (!esMappingModel) {
        return HitDetailsBaseResponse.failed();
      }

      const body = {
        from: (pageIndex && pageIndex > 0 ? pageIndex - 1 : 0) * pageSize,
        size: pageSize || 10,
        sort: { [sortField]: { order: sortOrder } },
        query: {
          bool: {
            // 非自然人数据
            must_not: [{ term: { comporg: '2' } }],
            filter: [{ term: { type: esMappingModel.type } }, { terms: { riskid: params.ids } }],
          },
        },
      };

      const response = await this.searchEs(body, dimensionKey);

      const resp = Object.assign(new HitDetailsBaseResponse(), {
        Paging: {
          PageSize: pageSize,
          PageIndex: pageIndex,
          TotalRecords: response?.body?.hits?.total?.value || 0,
        },
        Result: response?.body?.hits?.hits?.map((d) => d._source) || [],
      });

      if (resp?.Result?.length) {
        this.formatResponse(resp);
      }
      return resp;
    } catch (error) {
      this.logger.error(`getDateByIds dimensionKey: ${dimensionKey} error: `, error.merssage);
      this.logger.error(error);
      return HitDetailsBaseResponse.failed();
    }
  }
}
